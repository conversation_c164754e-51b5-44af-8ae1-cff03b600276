<?php

declare(strict_types=1);

namespace tools\deckanalyzer;

/**
 * Clase DeckAnalyzer - Analiza mazos de Clash Royale para evaluar su efectividad y estrategias.
 *
 * Esta clase proporciona funcionalidades para analizar mazos de Clash Royale,
 * evaluando sus sinergias, versatilidad, balance de ataque/defensa y otras métricas importantes.
 */
class DeckAnalyzer
{
    /** @var object El mazo que se está analizando */
    public object $Deck;

    /** @var object Constantes utilizadas para los cálculos de análisis */
    private object $constants;

    /** @var array Estrategias disponibles para el análisis */
    private array $strategies;

    /** @var string Tipo de análisis a realizar ('basic', 'intermediate', 'advanced') */
    public string $type;
    public array $allCardsInstanceLevel11;

    public function __construct(object $statCards, object $constants, array $pathsStrategies, object $instanceDeck, string $type)
    {
        $allowedTypes = ['basic', 'intermediate', 'advanced'];
        if (!in_array($type, $allowedTypes))
            throw new \Exception("Invalid type: {$type}. Allowed types are: " . implode(', ', $allowedTypes));

        if (!isset($instanceDeck->Cards) || !is_array($instanceDeck->Cards))
            throw new \Exception("Invalid Deck instance: 'Cards' property is missing or not an array.");

        if (!isset($instanceDeck->CardsEvo) || !is_array($instanceDeck->CardsEvo))
            throw new \Exception("Invalid Deck instance: 'CardsEvo' property is missing or not an array.");

        $this->constants = $constants;
        $this->pathsStrategies = $pathsStrategies;
        $this->Deck = $instanceDeck;
        $this->type = $type;

        foreach ($pathsStrategies as $keyPath => $path) {
            if (!file_exists($path)) {
                throw new \Exception("El archivo de estrategia no existe: {$path}");
            }

            $pathData = file_get_contents($path);
            if ($pathData === false) {
                throw new \Exception("No se pudo leer el archivo de estrategia: {$path}");
            }

            $this->strategies[$keyPath] = json_decode(json: $pathData);
            if ($this->strategies[$keyPath] === null) {
                throw new \Exception("Error al decodificar el JSON del archivo de estrategia: " . json_last_error_msg());
            }
        }

        foreach (array_merge($statCards->cards, $statCards->towerCards) as $card) {
            // Ensure stats are at level 11 for consistent analysis
            $card->damage = $card->damage->level11 ?? 0;
            $card->dps = $card->dps->level11 ?? 0;
            $card->hitpoints = $card->hitpoints->level11 ?? 0;
            // Handle potential missing tower damage
            if (isset($card->TowerDamage)) {
                $card->towerDamage = $card->TowerDamage->level11 ?? 0;
            }
            $this->allCardsInstanceLevel11[] = $card;
        }
    }

    /**
     * Analiza los promedios de sinergia del mazo, considerando sinergias de cartas y estrategias.
     *
     * @return array Retorna un array con los siguientes elementos:
     *               - 'totalScore': Promedio total de sinergia.
     *               - 'totalPointsSynergyCards': Puntos de sinergia totales entre cartas.
     *               - 'totalPointsSynergyStrategy': Puntos de sinergia totales con la estrategia.
     *               - 'totalPointsSynergyCards': Puntos de sinergia totales entre cartas.
     *               - 'totalPointsSynergyStrategy': Puntos de sinergia totales con la estrategia.
     *               - 'arrayTotalPointsSynergyStrategy': Array con los puntos de sinergia de estrategia por carta.
     *               - 'arrayTotalPointsSynergyCards': Array con los puntos de sinergia entre cartas por carta.
     *               - 'maximumPossiblePointsStrategy': Máximos puntos posibles de sinergia de estrategia.
     *               - 'maximumPossiblePointsCards': Máximos puntos posibles de sinergia entre cartas.
     *               - 'arraySynergyCards': Detalles de sinergia entre cartas.
     *               - 'arraySynergyStrategy': Detalles de sinergia de estrategia.
     *               - 'msg': Mensajes informativos o de advertencia sobre la sinergia.
     * @throws \Exception Si ocurre un error al buscar sinergias o decodificar datos.
     */
    public function getScoreSynergy(): array
    {
        $messages = $arrayTotalPointsSynergyStrategy = $arrayTotalPointsSynergyCards =
            $arraySynergyCards = $arraySynergyStrategy = [];
        $totalNumCards = 0; // Initialize totalNumCards, max points calculated later

        // Refactored loop using foreach with index (though index is not strictly used here)
        foreach (array_merge($this->Deck->Cards, $this->Deck->CardsEvo) as $index => $card) {
            $totalNumCards++;

            $synergyCards = $this->searchSynergiesCard(
                $card,
                'Analisis',
                'Cards'
            );
            // Store synergy details for cards
            $arraySynergyCards[] = array_merge($synergyCards, ['name' => $card->name, 'medium' => $card->urlIcon]);

            $synergyStrategy = $this->searchSynergiesCard(
                $card,
                'Analisis',
                'General'
            );
            // Store synergy details for strategy
            $arraySynergyStrategy[] = array_merge($synergyStrategy, ['name' => $card->name, 'medium' => $card->urlIcon]);

            // Store individual synergy points
            $arrayTotalPointsSynergyCards[] = $synergyCards['pointsSynergy'];
            $arrayTotalPointsSynergyStrategy[] = $synergyStrategy['pointsSynergy'];
            // Removed in-loop calculation for maximum possible points
        }

        // Calculate maximum possible points after the loop using max(array_column(...))
        // Added checks for empty arrays to prevent errors/warnings
        $maximumPossiblePointsCards = !empty($arraySynergyCards) ? max(array_column($arraySynergyCards, 'maximumPossiblePoints')) : 0;
        $maximumPossiblePointsStrategy = !empty($arraySynergyStrategy) ? max(array_column($arraySynergyStrategy, 'maximumPossiblePoints')) : 0;

        // Continue with existing logic after loop
        count($arrayTotalPointsSynergyStrategy) != $totalNumCards &&
            $messages[] = '<span class="cs-color-GoldenYellow text-center">INFO: No todas las cartas de tu mazo se integran completamente con la estrategia general, "' . ($totalNumCards - count($arrayTotalPointsSynergyStrategy)) . ' cartas" no presentan sinergias en este análisis.</span>';

        count($arrayTotalPointsSynergyCards) != $totalNumCards &&
            $messages[] = '<span class="cs-color-GoldenYellow text-center">INFO: No todas las cartas de tu mazo tienen sinergias directas, "' . ($totalNumCards - count($arrayTotalPointsSynergyCards)) . ' cartas" no presentan sinergias en este análisis.</span>';

        // Avoid division by zero if arrays are empty
        $averageStrategy = !empty($arrayTotalPointsSynergyStrategy) ? round(array_sum($arrayTotalPointsSynergyStrategy) / count($arrayTotalPointsSynergyStrategy)) : 0;
        $averageCards = !empty($arrayTotalPointsSynergyCards) ? round(array_sum($arrayTotalPointsSynergyCards) / count($arrayTotalPointsSynergyCards)) : 0;

        // Avoid division by zero for scores
        $scoreStrategy = ($maximumPossiblePointsStrategy > 0) ? round(($averageStrategy / ($maximumPossiblePointsStrategy * 0.5)) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
        $scoreCards = ($maximumPossiblePointsCards > 0) ? round(num: ($averageCards / ($maximumPossiblePointsCards * 0.5)) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
        $scoreStrategyTotalSynergy = ($totalNumCards > 0) ? round(((count($arrayTotalPointsSynergyStrategy) / $totalNumCards) * 0.75) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
        $scoreCardsTotalSynergy = ($totalNumCards > 0) ? round(((count($arrayTotalPointsSynergyCards) / $totalNumCards) * 0.75) * $this->constants->PERCENTAGE_MULTIPLIER) : 0;

        $totalScore = round(($scoreStrategy + $scoreCards + $scoreStrategyTotalSynergy + $scoreCardsTotalSynergy) / $this->constants->SYNERGY_AVERAGE_DIVISOR);

        if ($totalScore < 25) {
            $messages[] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: La sinergia total del mazo es muy baja (' . $totalScore . '). Considera revisar las sinergias entre cartas y con la estrategia general.</span>';
        } elseif ($totalScore < 50) {
            $messages[] = '<span class="cs-color-GoldenYellow text-center">INFO: La sinergia total del mazo es moderada (' . $totalScore . '). Podrías mejorarla revisando las sinergias individuales.</span>';
        }

        // Calculate top positive and negative synergies
        $allSynergies = [];
        // Strategy Synergies
        foreach ($arraySynergyStrategy as $strategySynergy) {
            $allSynergies[] = [
                'context' => $strategySynergy['name'] . ' <-> Strategy',
                'score' => $strategySynergy['pointsSynergy']
            ];
        }
        // Card-to-Card Synergies
        $allDeckCards = array_merge($this->Deck->Cards, $this->Deck->CardsEvo); // Cache deck cards
        foreach ($arraySynergyCards as $cardSynergy) {
            foreach ($cardSynergy['SynergyCards'] as $cardDeckKey => $interaction) {
                // Ensure the key exists before accessing
                $targetCardName = isset($allDeckCards[$cardDeckKey]) ? $allDeckCards[$cardDeckKey]->name : 'Unknown Card (' . $cardDeckKey . ')';
                $allSynergies[] = [
                    'context' => $cardSynergy['name'] . ' <-> ' . $targetCardName,
                    'score' => $interaction['pointsSynergy']
                ];
            }
        }

        // Sort ascending by score to find lowest (most negative or least positive)
        usort($allSynergies, fn($a, $b) => $a['score'] <=> $b['score']);
        $topNegativeSynergies = array_slice($allSynergies, 0, 3);

        // Sort descending by score to find highest (most positive)
        usort($allSynergies, fn($a, $b) => $b['score'] <=> $a['score']);
        $topPositiveSynergies = array_slice($allSynergies, 0, 3);

        return [
            "totalScore" => $totalScore,
            "totalPointsSynergyCards" => array_sum($arrayTotalPointsSynergyCards),
            "totalPointsSynergyStrategy" => array_sum($arrayTotalPointsSynergyStrategy),
            "arrayTotalPointsSynergyStrategy" => $arrayTotalPointsSynergyStrategy,
            "arrayTotalPointsSynergyCards" => $arrayTotalPointsSynergyCards,
            "maximumPossiblePointsStrategy" => $maximumPossiblePointsStrategy,
            "maximumPossiblePointsCards" => $maximumPossiblePointsCards,
            "arraySynergyCards" => $arraySynergyCards,
            "arraySynergyStrategy" => $arraySynergyStrategy,
            'msg' => $messages,
            'topPositiveSynergies' => $topPositiveSynergies, // Added top positive synergies
            'topNegativeSynergies' => $topNegativeSynergies // Added top negative synergies
        ];
    }

    /**
     * Analiza la versatilidad del mazo evaluando la cobertura de diferentes tipos de ataque.
     *
     * Este método evalúa la versatilidad del mazo mediante la cobertura de diferentes tipos de ataque,
     * asegurando que el mazo tenga una buena distribución de cartas que cubran diversas estrategias.
     * @return array Retorna un array con los resultados del análisis y la versatilidad:
     *               - 'totalScore': Puntuación total de versatilidad (0-100).
     *               - 'cardwin': Array de objetos Card de condición de victoria.
     *               - 'cardter': Array de objetos Card terrestres.
     *               - 'cardaer': Array de objetos Card aéreos.
     *               - 'carddew': Array de objetos Card defensivos/win condition.
     *               - 'cardhech': Array asociativo de objetos Card de hechizos agrupados por tipo ('hech1', 'hech2', etc.).
     *               - 'msg': Mensajes informativos o de advertencia.
     * @throws \Exception Si ocurre un error al determinar el grupo de una carta.
     */
    public function getScoreVersatility(): array
    {
        $versatilityResult = [
            'totalScore' => 0,
            'cardwin' => [],
            'cardter' => [],
            'cardaer' => [],
            'carddew' => [],
            'cardhech' => [
                'hech0' => [],
                'hech1' => [],
                'hech2' => [],
                'hech3' => [],
                'hech4' => []
            ],
            'msg' => []
        ];

        foreach (array_merge($this->Deck->Cards, $this->Deck->CardsEvo) as $card) {
            $groupCard = $this->getGroupCard($card);
            if ($groupCard != 'hech') {
                $versatilityResult["card{$groupCard}"][] = $card;
            } elseif (isset($card->Attack[0]) && isset($versatilityResult["cardhech"][$card->Attack[0]])) {
                // Check if Attack[0] exists and is a valid key for hechizos
                $versatilityResult["cardhech"][$card->Attack[0]][] = $card;
            } else {
                // Handle cases where spell type is unexpected or missing
                // Maybe log a warning or assign to a default category like 'hech0'
                $versatilityResult["cardhech"]['hech0'][] = $card;
                // Optionally add a message:
                // $versatilityResult['msg'][] = '<span class="cs-color-GoldenYellow text-center">INFO: Spell "' . $card->name . '" has an unrecognized type (' . ($card->Attack[0] ?? 'N/A') . ') and was categorized as generic.</span>';
            }
        }


        $totalVersatility = 0;
        foreach (['cardwin', 'cardter', 'cardaer', 'carddew'] as $type) {
            if (!empty($versatilityResult[$type])) {
                $totalVersatility++;
            } else {
                $versatilityResult['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTINCIA: No hay cartas de tipo ' . $type . ' en el Mazo. Esto puede afectar la versatilidad.</span>';
            }
        }

        $spellsInDeck = [];
        foreach ($versatilityResult['cardhech'] as $type => $spells) {
            foreach ($spells as $spell) {
                // Ensure Attack[0] exists before adding
                if (isset($spell->Attack[0])) {
                    $spellsInDeck[] = $spell->Attack[0];
                }
            }
        }
        $spellsInDeck = array_unique($spellsInDeck); // Avoid duplicates if a spell fits multiple internal types


        $spellVersatility = false;
        foreach ($this->constants->ARRAYS_COMBINATION_SPELL as $type => $combination) {
            // Check if the deck contains *all* spells required by the combination
            if (!empty($combination) && empty(array_diff($combination, $spellsInDeck))) {
                $spellVersatility = true;
                break; // Found a valid combination
            }
        }


        if ($spellVersatility) {
            $totalVersatility++;
        } else {
            $versatilityResult['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTINCIA: La combinación de hechizos no es versátil.</span>';
            $versatilityResult['msg'][] = '<span class="cs-color-GoldenYellow text-center">INFO: Revisa que no haya hechizos con características similares (daño, elixir, etc.).</span>';
        }

        // Avoid division by zero if NUM_CARD_TYPES_VERSATILITY is 0
        $divisor = $this->constants->NUM_CARD_TYPES_VERSATILITY * $this->constants->DIVISOR_VERSATILITY;
        $versatilityResult['totalScore'] = ($divisor > 0)
            ? round((($totalVersatility / $this->constants->NUM_CARD_TYPES_VERSATILITY) / $this->constants->DIVISOR_VERSATILITY) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;


        return $versatilityResult;
    }

    /**
     * Calcula los promedios de ataque o defensa del mazo, considerando estadísticas como DPS, daño y puntos de vida.
     *
     * Este método itera a través de las cartas del mazo, diferenciando entre cartas de ataque y defensa,
     * y aplicando cálculos específicos para cada tipo. También considera el daño de los hechizos de forma separada.
     * Los cálculos tienen en cuenta si la carta tiene evolución y el nivel de las estadísticas relevantes.
     *
     * @param string $type El tipo de promedio a calcular: 'attack' para ataque, 'defense' para defensa.
     * @return array Un array asociativo que contiene:
     *               - 'totalScore': Promedio total de ataque o defensa, ponderando diferentes estadísticas.
     *               - 'totalDamageSpell': Daño total de todos los hechizos en el mazo.
     *               - 'numCardsDamageSpell': Número de hechizos que infligen daño en el mazo.
     *               - 'msg':  Mensajes informativos o de advertencia (actualmente sin uso en esta función).
     *               Además, incluye cálculos internos para promedios de DPS, daño y puntos de vida,
     *               ajustados según el tipo de cálculo ('attack' o 'defense').
     * @throws \InvalidArgumentException Si el `$type` proporcionado no es 'attack' o 'defense'.
     * @throws \Exception Si ocurre un error al calcular los promedios o roles de batalla.
     */
    public function getBattleRolesScores(string $type): array
    {
        if (!in_array($type, ['attack', 'defense'])) {
            throw new \InvalidArgumentException("Invalid type: {$type}. Allowed types are: attack, defense");
        }

        $stats = [
            'totalScore' => 0,
            'scoreDps' => 0, // Añadido
            'scoreHitpoints' => 0, // Añadido
            'scoreRange' => 0, // Añadido
            'msg' => []
        ];

        // Use only non-tower cards for general role scoring
        $deckCardsForRoles = array_filter(array_merge($this->Deck->Cards, $this->Deck->CardsEvo), fn($card) => $card->type !== 'Tower');

        $averages = $this->getAverages($deckCardsForRoles, $type);
        // Use all non-tower cards as reference
        $allCardsForReference = array_filter($this->allCardsInstanceLevel11, fn($card) => $card->type !== 'Tower');
        $referenceStats = $this->getAverages($allCardsForReference); // Use 'all' roles for reference
        $ponderation = 1.5; // Ponderación para ajustar la dificultad de alcanzar un buen puntaje

        // Calcular puntajes individuales normalizados (0-100+)
        $stats['scoreDps'] = $averages['dps'] > 0 && $referenceStats['dps'] > 0
            ? round(($averages['dps'] / ($referenceStats['dps'] * $ponderation)) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;
        $stats['scoreHitpoints'] = $averages['hitpoints'] > 0 && $referenceStats['hitpoints'] > 0
            ? round(($averages['hitpoints'] / ($referenceStats['hitpoints'] * $ponderation)) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;
        $stats['scoreRange'] = $averages['range'] > 0 && $referenceStats['range'] > 0
            ? round(($averages['range'] / ($referenceStats['range'] * $ponderation)) * $this->constants->PERCENTAGE_MULTIPLIER)
            : 0;

        // Calcular puntaje total como promedio de los puntajes individuales
        $scores = [
            $stats['scoreHitpoints'],
            $stats['scoreDps'],
            $stats['scoreRange']
        ];
        // Filtrar scores de 0 para no afectar el promedio si una estadística no aplica (ej. rango en melee)
        $validScores = array_filter($scores, fn($score) => $score > 0);
        $stats['totalScore'] = !empty($validScores) ? round(array_sum($validScores) / count($validScores)) : 0;


        // Advertencias generales basadas en el score total
        if ($stats['totalScore'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: El mazo tiene un bajo nivel general de "' . $type . '" (' . $stats['totalScore'] . '). Considera agregar más cartas especializadas en esta área.</span>';
        }

        // Advertencias específicas basadas en puntajes individuales
        if ($stats['scoreDps'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: El DPS promedio de ' . $type . ' del mazo es bajo (' . $stats['scoreDps'] . ').</span>';
        }
        if ($stats['scoreHitpoints'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: Los puntos de vida promedio de ' . $type . ' del mazo son bajos (' . $stats['scoreHitpoints'] . ').</span>';
        }
        if ($stats['scoreRange'] < 25) {
            $stats['msg'][] = '<span class="cs-color-IntenseOrange text-center">ADVERTENCIA: El rango promedio de ' . $type . ' del mazo es bajo (' . $stats['scoreRange'] . ').</span>';
        }

        return $stats;
    }

    /**
     * Calcula los promedios de ataque y defensa por grupos de cartas
     * Calcula los porcentajes de contribución al ataque y defensa por grupos de cartas (win condition, terrestre, aéreo, defensivo/win, hechizo).
     *
     * Utiliza los grupos de cartas obtenidos del análisis de versatilidad y calcula qué porcentaje
     * del puntaje total de ataque y defensa aporta cada grupo.
     *
     * @param array $versatilityGroups Array asociativo con las cartas agrupadas por tipo (resultado de `getScoreVersatility`).
     *                                 Ej: ['cardwin' => [...], 'cardter' => [...], ... 'cardhech' => ['hech1' => [...], ...]]
     * @return array Un array asociativo donde las claves son los tipos de grupo ('cardwin', 'cardter', etc.)
     *               y los valores son arrays con los porcentajes de 'ataque' y 'defensa' como strings (ej. '25%').
     * @throws \Exception Si ocurre un error al calcular los promedios de los grupos o al acceder a constantes.
     */
    public function getBattleRolesScoresByGroup(array $versatilityGroups): array
    {
        $numCardsDamageSpell = $totalDamageSpell = 0;
        $allSpells = [];
        foreach ($versatilityGroups['cardhech'] as $spellGroup) {
            $numCardsDamageSpell += count($spellGroup);
            foreach ($spellGroup as $spell) {
                $totalDamageSpell += $spell->damage ?? 0; // Use null coalescing for safety
                $allSpells[] = $spell; // Collect all spell objects
            }
        }

        // Use getAverages for spells to consider their roles
        $spellAveragesAttack = $this->getAverages($allSpells, 'attack');
        $spellAveragesDefense = $this->getAverages($allSpells, 'defense');

        // Use reference stats for normalization (consider using spell-specific reference if available)
        $allCardsForReference = array_filter($this->allCardsInstanceLevel11, fn($card) => $card->type !== 'Tower');
        $referenceStats = $this->getAverages($allCardsForReference); // Use 'all' roles for reference

        $calculateScore = function (array $averages) use ($referenceStats): float {
            $avgHitpoints = ($referenceStats['hitpoints'] > 0) ? ($averages['hitpoints'] / $referenceStats['hitpoints'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
            $avgDps = ($referenceStats['dps'] > 0) ? ($averages['dps'] / $referenceStats['dps'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
            $avgRange = ($referenceStats['range'] > 0) ? ($averages['range'] / $referenceStats['range'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
            // Damage might be more relevant for spells than DPS/Hitpoints
            $avgDamage = ($referenceStats['damage'] > 0) ? ($averages['damage'] / $referenceStats['damage'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;

            // Adjust weighting for spells - prioritize damage, maybe range
            $validScores = array_filter([$avgDamage, $avgRange * 0.5]); // Example: Weight damage higher
            return !empty($validScores) ? round(array_sum($validScores) / count($validScores)) : 0;
            // Original calculation: return ($avgHitpoints + $avgDps + $avgRange) / $this->constants->WEIGHTED_AVERAGE_DIVISOR;
        };

        $spellAttackScore = $calculateScore($spellAveragesAttack);
        $spellDefenseScore = $calculateScore($spellAveragesDefense);


        $averagesByGroup = [
            'cardwin' => ['ataque' => 0, 'defensa' => 0],
            'cardter' => ['ataque' => 0, 'defensa' => 0],
            'cardaer' => ['ataque' => 0, 'defensa' => 0],
            'carddew' => ['ataque' => 0, 'defensa' => 0]
        ];

        $groupTypes = ['cardwin', 'cardter', 'cardaer', 'carddew'];
        $totalAttack = $spellAttackScore; // Start with spell score
        $totalDefense = $spellDefenseScore; // Start with spell score

        foreach ($groupTypes as $groupType) {
            $group = $versatilityGroups[$groupType] ?? []; // Use null coalescing
            $count = count($group);

            if ($count === 0) {
                $averagesByGroup[$groupType] = ['ataque' => 0, 'defensa' => 0];
                continue;
            }

            // Recalculate score using the same logic as getBattleRolesScores
            $calculateGroupScore = function (array $averages) use ($referenceStats): float {
                $avgHitpoints = ($referenceStats['hitpoints'] > 0) ? ($averages['hitpoints'] / $referenceStats['hitpoints'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
                $avgDps = ($referenceStats['dps'] > 0) ? ($averages['dps'] / $referenceStats['dps'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;
                $avgRange = ($referenceStats['range'] > 0) ? ($averages['range'] / $referenceStats['range'] * $this->constants->PERCENTAGE_MULTIPLIER) : 0;

                $validScores = array_filter([$avgHitpoints, $avgDps, $avgRange]);
                return !empty($validScores) ? round(array_sum($validScores) / count($validScores)) : 0;
                // Original: return ($avgHitpoints + $avgDps + $avgRange) / $this->constants->WEIGHTED_AVERAGE_DIVISOR;
            };


            $attackScore = $calculateGroupScore($this->getAverages($group, 'attack'));
            $defenseScore = $calculateGroupScore($this->getAverages($group, 'defense'));

            $averagesByGroup[$groupType] = ['ataque' => $attackScore, 'defensa' => $defenseScore];

            $totalAttack += $attackScore;
            $totalDefense += $defenseScore;
        }

        // Add spell scores to the group array
        $averagesByGroup['cardhech'] = ['ataque' => $spellAttackScore, 'defensa' => $spellDefenseScore];

        // Calculate percentages
        foreach ($averagesByGroup as &$averages) {
            $averages['ataque'] = $totalAttack > 0 ? round(($averages['ataque'] / $totalAttack) * 100) . '%' : '0%';
            $averages['defensa'] = $totalDefense > 0 ? round(($averages['defensa'] / $totalDefense) * 100) . '%' : '0%';
        }
        unset($averages); // Break reference

        return $averagesByGroup;
    }


    /**
     * Calcula el costo total de elixir del ciclo corto del mazo.
     *
     * Ordena las cartas del mazo por costo de elixir de manera ascendente
     * y luego suma el costo de elixir de las cuatro cartas más baratas.
     * @return int El costo total de elixir de las 4 cartas más baratas (excluyendo cartas de tipo 'Tower').
     */
    public function getShortCycle(): int
    {
        // Include evo cards in cycle calculation
        $cards = array_filter(array_merge($this->Deck->Cards, $this->Deck->CardsEvo), function ($card) {
            return $card->type != 'Tower';
        });

        usort($cards, function ($a, $b) {
            return $a->elixirCost <=> $b->elixirCost;
        });

        $shortCycle = array_slice($cards, 0, 4);
        $totalElixir = array_sum(array_column($shortCycle, 'elixirCost'));

        return $totalElixir;
    }

    /**
     * Analiza las sinergias de una carta específica con el resto del mazo
     * Busca y calcula las sinergias de una carta específica dentro del contexto del mazo y una estrategia dada.
     *
     * Analiza las interacciones de la carta proporcionada con las demás cartas del mazo
     * y con las condiciones generales de la estrategia seleccionada. Utiliza `eval` para procesar condiciones dinámicas.
     *
     * @param object $card La instancia de la carta a analizar.
     * @param string $typeStrategy El tipo de estrategia a considerar para el análisis (ej. 'Analisis', 'Control', 'Ciclo').
     *                             Debe ser una clave válida en `$strategiesMap`.
     * @param string $typeSyn El tipo de sinergia a buscar: 'Cards' (solo entre cartas), 'General' (solo con la estrategia),
     *                        o 'All' (ambas). Por defecto es 'All'.
     * @return array Un array asociativo con los resultados de la búsqueda de sinergias:
     *               - 'pointsSynergy': Puntuación total de sinergia acumulada.
     *               - 'maximumPossiblePoints': Máxima puntuación de sinergia posible según las condiciones evaluadas.
     *               - 'msg': Mensajes descriptivos de las sinergias generales encontradas (obsoleto, usar 'reasons').
     *               - 'reasons': Array de arrays, cada uno con 'reason' (descripción) y 'points' para sinergias generales.
     *               - 'SynergyCards': Array asociativo donde las claves son índices de cartas del mazo y los valores
     *                                 son arrays con 'medium' (URL del icono), 'pointsSynergy' (puntos de esa interacción),
     *                                 'msg' (mensajes de esa interacción específica, obsoleto, usar 'reasons') y
     *                                 'reasons' (array de arrays con 'reason' y 'points' para esa interacción).
     * @throws \InvalidArgumentException Si `$typeStrategy` no es una estrategia válida.
     * @throws \Exception Si ocurre un error durante la evaluación de condiciones (`eval`), al decodificar JSON,
     *                    al acceder a datos de cartas o estrategias, o al calcular estadísticas básicas del mazo.
     * @throws \Error Si `eval()` produce un error irrecuperable.
     */
    public function searchSynergiesCard(object $card, string $typeStrategy, string $typeSyn = 'All'): array
    {
        $strategiesMap = [
            'Analisis' => ['General', 'Defensa', 'Precion', 'Adaptavilidad'],
            'Control' => ['General', 'Defensa', 'Precion', 'Adaptavilidad'],
            'Ciclo' => ['General', 'Defensa', 'Adaptavilidad', 'Ciclo', 'Ciclo'],
            'Push' => ['General', 'Defensa', 'Adaptavilidad', 'Push', 'Push'],
            'Split Push' => ['General', 'Defensa', 'Adaptavilidad', 'SplitPush', 'SplitPush'],
            'Agresivo' => ['General', 'Defensa', 'Precion', 'Adaptavilidad', 'Agresivo', 'Agresivo'],
        ];

        if (!isset($strategiesMap[$typeStrategy]))
            throw new \InvalidArgumentException("Invalid strategy: {$typeStrategy}.");

        $statSyn = [
            'pointsSynergy' => 0.0,
            'maximumPossiblePoints' => 0,
            'msg' => [], // General messages/logs
            'reasons' => [], // Reasons for general synergies
            'SynergyCards' => [] // Details about card-specific synergies
        ];

        $allDeckCards = array_merge($this->Deck->Cards, $this->Deck->CardsEvo);
        $DeckStatsArray = json_decode(json_encode(value: $allDeckCards), true);
        $staMaz = static::deckStatsBasic($allDeckCards);
        $structureInMazo = in_array('Building', array_column($DeckStatsArray, 'type'));
        $DefWinInMazo = array_filter($allDeckCards, function ($card) {
            return $this->getGroupCard($card) == 'dew';
        });
        $arrspeedCom = ['null', 'Slow', 'Medium', 'Fast', 'Very Fast', 'null'];

        // Pre-calculate special keys for the card being analyzed
        $cardSpecialKeys = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
        $cardSpecialEvoKeys = ($card->evolution && $card->specialEvo) ? array_keys(json_decode(json_encode($card->specialEvo), true)) : [];
        $cardChampeonAbilityKeys = ($card->rarity == 'Champion' && isset($card->special->champeonAbility)) ? array_keys(json_decode(json_encode($card->special->champeonAbility), true)) : [];


        $statCard = [
            'groupCard' => $this->getGroupCard($card),
            'specialKeys' => $cardSpecialKeys,
            'specialEvoKeys' => $cardSpecialEvoKeys,
            'champeonAbilityKeys' => $cardChampeonAbilityKeys,
            'diehech' => $this->calculateDiehech($card->hitpoints)
        ];

        foreach ($strategiesMap[$typeStrategy] as $strategyMapName) { // iterate over each strategy in the map
            if ($typeSyn == 'Cards' || $typeSyn == 'All') {
                foreach ($allDeckCards as $cardDeckKey => $cardDeck) { // iterate over each card in the deck
                    if ($cardDeck->name == $card->name)
                        continue;

                    // Initialize the entry for this specific card interaction if it doesn't exist
                    if (!isset($statSyn['SynergyCards'][$cardDeckKey])) {
                        $statSyn['SynergyCards'][$cardDeckKey] = [
                            'medium' => $cardDeck->urlIcon,
                            'pointsSynergy' => 0.0,
                            'msg' => [], // Kept for potential backward compatibility or debugging
                            'reasons' => [] // Initialize reasons for this specific card interaction
                        ];
                    }

                    // Pre-calculate special keys for the deck card
                    $deckCardSpecialKeys = $cardDeck->special ? array_keys(json_decode(json_encode($cardDeck->special), true)) : [];
                    $deckCardSpecialEvoKeys = ($cardDeck->evolution && $cardDeck->specialEvo) ? array_keys(json_decode(json_encode($cardDeck->specialEvo), true)) : [];
                    $deckCardChampeonAbilityKeys = ($cardDeck->rarity == 'Champion' && isset($cardDeck->special->champeonAbility)) ? array_keys(json_decode(json_encode($cardDeck->special->champeonAbility), true)) : [];


                    $statCardDeck = [
                        'groupCard' => $this->getGroupCard($cardDeck),
                        'specialKeys' => $deckCardSpecialKeys,
                        'specialEvoKeys' => $deckCardSpecialEvoKeys,
                        'champeonAbilityKeys' => $deckCardChampeonAbilityKeys,
                        'diehech' => $this->calculateDiehech($cardDeck->hitpoints),
                        'speedSyn' => array_slice($arrspeedCom, (array_search($cardDeck->speed ?? 'Medium', $arrspeedCom) - 1), 3) // Added default speed
                    ];

                    foreach ($this->strategies[$strategyMapName]->Cards as $Condition) { // iterate over each condition in the strategy
                        try {
                            // Suppress errors during eval and check the result later
                            $conditionResult = @eval ("return " . $Condition->condition . ";");
                            if ($conditionResult === false && error_get_last() !== null) {
                                $error = error_get_last();
                                // Log the specific error from eval
                                error_log("Error evaluating condition for card synergy: {$Condition->condition} - Error: {$error['message']} in {$error['file']} on line {$error['line']}");
                                error_clear_last(); // Clear the error after logging
                                continue; // Skip this condition
                            }
                        } catch (\ParseError $e) {
                            // Log or handle the eval parse error specifically
                            error_log("ParseError evaluating condition for card synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                            continue; // Skip this condition
                        } catch (\Throwable $e) {
                            // Catch other potential errors during eval
                            error_log("Error evaluating condition for card synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                            continue; // Skip this condition
                        }


                        if ($conditionResult) {
                            // Accumulate points for the overall card synergy and specific interaction
                            $pointsToAdd = $Condition->points ?? 0; // Default to 0 if points missing
                            $statSyn['pointsSynergy'] += $pointsToAdd;
                            $statSyn['SynergyCards'][$cardDeckKey]['pointsSynergy'] += $pointsToAdd;

                            // Store the descriptive message (optional, can be kept or removed)
                            $statSyn['SynergyCards'][$cardDeckKey]['msg'][] = $strategyMapName . ': ' . ($pointsToAdd >= 0 ? '+' : '') . $pointsToAdd . 'pts ' . $Condition->name;

                            // Store the reason and points for this specific synergy
                            $statSyn['SynergyCards'][$cardDeckKey]['reasons'][] = ['reason' => $Condition->name, 'points' => $pointsToAdd];
                        }
                        // Accumulate maximum possible points regardless of condition result
                        // Ensure points is numeric before using abs()
                        if (isset($Condition->points) && is_numeric($Condition->points)) {
                            $statSyn['maximumPossiblePoints'] += abs($Condition->points); // Use abs() to sum potential positive impact
                        } else {
                            // Log or handle non-numeric/missing points if necessary
                            error_log("Non-numeric or missing points found for condition '{$Condition->name}' in strategy '{$strategyMapName}'.");
                        }
                    }
                }
            }

            if ($typeSyn == 'General' || $typeSyn == 'All') {
                foreach ($this->strategies[$strategyMapName]->General as $Condition) {
                    try {
                        // Suppress errors during eval and check the result later
                        $conditionResult = @eval ("return " . $Condition->condition . ";");
                        if ($conditionResult === false && error_get_last() !== null) {
                            $error = error_get_last();
                            // Log the specific error from eval
                            error_log("Error evaluating condition for general synergy: {$Condition->condition} - Error: {$error['message']} in {$error['file']} on line {$error['line']}");
                            error_clear_last(); // Clear the error after logging
                            continue; // Skip this condition
                        }
                    } catch (\ParseError $e) {
                        // Log or handle the eval parse error specifically
                        error_log("ParseError evaluating condition for general synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                        continue; // Skip this condition
                    } catch (\Throwable $e) {
                        // Catch other potential errors during eval
                        error_log("Error evaluating condition for general synergy: {$Condition->condition} - Error: {$e->getMessage()}");
                        continue; // Skip this condition
                    }


                    if ($conditionResult) {
                        // Accumulate points for the overall synergy
                        $pointsToAdd = $Condition->points ?? 0; // Default to 0 if points missing
                        $statSyn['pointsSynergy'] += $pointsToAdd;

                        // Store the descriptive message (optional, can be kept or removed)
                        $statSyn['msg'][] = $strategyMapName . ': ' . ($pointsToAdd >= 0 ? '+' : '') . $pointsToAdd . 'pts ' . $Condition->name;

                        // Store the reason and points for this general synergy
                        $statSyn['reasons'][] = ['reason' => $Condition->name, 'points' => $pointsToAdd];
                    }
                    // Accumulate maximum possible points regardless of condition result
                    // Ensure points is numeric before using abs()
                    if (isset($Condition->points) && is_numeric($Condition->points)) {
                        $statSyn['maximumPossiblePoints'] += abs($Condition->points); // Use abs() to sum potential positive impact
                    } else {
                        // Log or handle non-numeric/missing points if necessary
                        error_log("Non-numeric or missing points found for general condition '{$Condition->name}' in strategy '{$strategyMapName}'.");
                    }
                }

            }
        }

        return $statSyn;
    }


    /**
     * Calcula estadísticas generales del mazo.
     *
     * Este método procesa un array de cartas que representan un mazo y calcula diversas
     * estadísticas agregadas sobre el mismo.
     *
     * @param array $Mazo0 Un array que representa el mazo de cartas.
     *
     * @return array Un array asociativo con las siguientes estadísticas del mazo:
     *   - elixirCostAll: (int) Costo total de elixir de todas las cartas en el mazo.
     *   - costElixTrops: (int) Costo total de elixir de las cartas que no son hechizos.
     *   - agresividad: (int|float) Suma total del DPS de las cartas que no son hechizos más el daño de los hechizos.
     *   - cardsSplash: (int) Cantidad de cartas con ataque de área (splash) que no son estructuras ('Building').
     *   - cardsUnique: (int) Cantidad de cartas que no tienen ataque de área y no son estructuras.
     *   - unitsAll: (int) Suma total de unidades de todas las cartas en el mazo.
     *   - synMaxAll: (int) Suma total de la sinergia máxima de todas las cartas en el mazo (si está presente).
     *   - spells: (array) Un array con las cartas de tipo 'Spell' en el mazo.
     * @throws \Exception Si ocurre un error al procesar los datos de las cartas (ej. decodificación JSON).
     */
    public static function deckStatsBasic(array $Mazo0): array
    {
        $Mazo = array_filter($Mazo0, fn($a) => $a); // Remove null/false entries
        // No need to re-encode/decode if $Mazo0 already contains objects or associative arrays
        // $Mazo = json_decode(json_encode($Mazo), true);

        foreach ($Mazo as $card) {
            if (!is_object($card) && !is_array($card) && !($card instanceof \stdClass)) {
                throw new \Exception("las cartas del Mazo en 'deckStatsBasic()' deben ser instancias de Card, " . gettype($card) . " encontrado");
            }
        }

        $stats = [
            'elixirCostAll' => 0,
            'costElixTrops' => 0,
            'agresividad' => 0,
            'cardsSplash' => 0,
            'cardsUnique' => 0,
            'unitsAll' => 0,
            'synMaxAll' => 0,
            'spells' => []
        ];

        foreach ($Mazo as $card) {
            $stats['elixirCostAll'] += $card->elixirCost ?? 0;
            $stats['unitsAll'] += $card->units ?? 1; // Default to 1 unit if not specified
            $stats['synMaxAll'] += $card->cardSynMax->synMax ?? 0; // Use null coalescing

            if (($card->type ?? '') == 'Spell') {
                $stats['spells'][] = $card;
                $stats['agresividad'] += $card->damage ?? 0; // Add spell damage to aggressiveness
            } else {
                // For non-spells
                if (($card->type ?? '') != 'Tower') {
                    $stats['costElixTrops'] += $card->elixirCost ?? 0;
                    $stats['agresividad'] += $card->dps ?? 0; // Add troop/building DPS to aggressiveness

                    if (($card->TypeAttack ?? 'unique') == 'splash' && ($card->type ?? '') != 'Building') {
                        $stats['cardsSplash']++;
                    } else {
                        $stats['cardsUnique']++;
                    }
                }
            }
        }


        return $stats;
    }

    /**
     * Calcula los promedios de estadísticas específicas para un conjunto de cartas.
     *
     * Este método itera sobre un array de cartas y calcula promedios para varias estadísticas
     * como DPS, daño, puntos de vida, velocidad de ataque y alcance. Permite filtrar
     * las cartas según su rol en batalla ('attack', 'defense' o 'all').
     *
     * @param array $cards Un array de objetos Card sobre los cuales se calcularán los promedios.
     * @param string $roleBatleCards El rol de las cartas a considerar en el cálculo.
     *                                 Puede ser 'all' (todas), 'attack' (ataque) o 'defense' (defensa).
     *                                 Por defecto es 'all'.
     *
     * @return array Un array asociativo con los promedios calculados para cada estadística
     *               ('dps', 'damage', 'hitpoints', 'hitspeed', 'range').  Los valores son
     *               redondeados a dos decimales.  Si no hay cartas para calcular una
     *               estadística en particular, el promedio para esa estadística será 0.
     * @throws \InvalidArgumentException Si el valor de `$roleBatleCards` no es 'all', 'attack' o 'defense'.
     * @throws \Exception Si ocurre un error al obtener el rol de batalla de una carta (`getCardBatleRole`).
     */
    public function getAverages(array $cards, string $roleBatleCards = 'all'): array
    {
        if (!in_array($roleBatleCards, ['all', 'attack', 'defense'])) {
            throw new \InvalidArgumentException("Invalid roleBatleCards: {$roleBatleCards}. Allowed roleBatleCards are: all, attack, defense");
        }

        $averages = $totals = $counts = array_fill_keys(['dps', 'damage', 'hitpoints', 'hitspeed', 'range'], 0);

        foreach ($cards as $card) {
            // Ensure card is an object with expected properties
            if (!is_object($card))
                continue;

            $cardRoleWeight = 1.0; // Default weight

            if ($roleBatleCards != 'all') {
                $roles = $this->getCardBatleRole($card); // Get percentages
                $roleWeight = ($roles[$roleBatleCards] ?? 0) / 100; // Weight based on the desired role percentage

                if ($roleWeight <= 0.1) { // Skip cards with very low relevance to the role (threshold adjustable)
                    continue;
                }
                $cardRoleWeight = $roleWeight; // Use the percentage as the weight
            }

            // Use null coalescing operator for safety
            $dps = $card->dps ?? 0;
            $damage = $card->damage ?? 0;
            $hitpoints = $card->hitpoints ?? 0;
            $hitspeed = $card->hitspeed ?? 0;
            $range = $card->range ?? ($card->radius ?? 0); // Consider radius if range is missing
            $type = $card->type ?? 'Unknown';
            $units = $card->units ?? 1;
            $suicide = $card->suicide ?? false;

            // Apply weight to totals
            if ($type != 'Spell' || ($type == 'Spell' && $units > 0)) { // Include unit-generating spells
                if (!$suicide) {
                    $totals['dps'] += $dps * $cardRoleWeight;
                    $counts['dps'] += $cardRoleWeight;
                    $totals['hitspeed'] += $hitspeed * $cardRoleWeight;
                    $counts['hitspeed'] += $cardRoleWeight;
                }
                if ($type != 'Tower') {
                    $totals['hitpoints'] += $hitpoints * $cardRoleWeight;
                    $counts['hitpoints'] += $cardRoleWeight;
                }
            }

            // Range applies to troops, buildings, and some spells (radius)
            if (is_numeric($range) && $range > 0) {
                $totals['range'] += $range * $cardRoleWeight;
                $counts['range'] += $cardRoleWeight;
            }

            // Damage applies to all card types that deal damage
            if ($damage > 0) {
                $totals['damage'] += $damage * $cardRoleWeight;
                $counts['damage'] += $cardRoleWeight;
            }

        }

        foreach ($totals as $averageType => $total) {
            if ($counts[$averageType] > 0) {
                $averages[$averageType] = round($total / $counts[$averageType], 2);
            }
        }

        return $averages;
    }


    /**
     * Calcula el promedio de coste de elixir del mazo, excluyendo torres.
     * @return float El costo promedio de elixir del mazo (excluyendo cartas de tipo 'Tower'), redondeado a un decimal.
     *               Retorna 0.0 si no hay cartas válidas para calcular el promedio (división por cero).
     */
    public function getAverageElixirCost(): float
    {
        // Include evo cards in calculation
        $cards = array_filter(array_merge($this->Deck->Cards, $this->Deck->CardsEvo), function ($card) {
            return $card->type != 'Tower';
        });

        $totalElixir = array_sum(array_column($cards, 'elixirCost'));
        $numCards = count($cards);

        // Avoid division by zero
        return $numCards > 0 ? round($totalElixir / $numCards, 1) : 0.0;
    }


    /**
     * Calcula el DPS de una carta
     * @param object $card Carta a analizar
     * @param array $param Parámetros adicionales de análisis
     * @return float DPS calculado
     */
    private function calculateDPS(object $card): int|float
    {
        $dps = 0;
        $special = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
        $differentUnits = isset($card->special->differentUnits) ? (array) $card->special->differentUnits : [];

        if (!empty($differentUnits)) {
            $dpsValues = array_map(function ($unitCount, $unitName) use ($card) {
                // Need a way to get card stats by name here. Assuming a helper or Card constructor logic.
                // This part might need adjustment based on how Card model fetches data.
                try {
                    $unitCard = array_filter(
                        $this->allCardsInstanceLevel11,
                        fn($card) => $card->name == $unitName
                    )[0] ?? null;
                    if (!$unitCard) {
                        throw new \Exception("Unit card not found for name: " . $unitName);
                    }
                    return ($unitCard->dps ?? 0) * $unitCount;
                } catch (\Exception $e) {
                    // Handle cases where the unit name doesn't correspond to a known card
                    error_log("Could not find card data for unit: " . $unitName . " in calculateDPS");
                    return 0;
                }
            }, $differentUnits, array_keys($differentUnits));
            $dps = array_sum($dpsValues);
        } elseif (in_array('dpsIncrement', $special)) {
            // DPS increment logic might be complex, using base DPS as a starting point
            $dps = ($card->dps ?? 0) * ($card->units ?? 1);
        } else {
            // Standard calculation
            $dps = ($card->dps ?? 0) * ($card->units ?? 1);
        }
        return $dps;
    }


    /**
     * Calcula el nivel de diehech de una carta
     * @param int $hitpoints Puntos de vida de la carta
     * @return string Nivel de diehech calculado
     */
    private function calculateDiehech(int $hitpoints): string|false
    {
        // Ensure constants are loaded and numeric
        $lvl4 = $this->constants->DIEHECH_LEVEL_4 ?? 0;
        $lvl3 = $this->constants->DIEHECH_LEVEL_3 ?? 0;
        $lvl2 = $this->constants->DIEHECH_LEVEL_2 ?? 0;
        $lvl1 = $this->constants->DIEHECH_LEVEL_1 ?? 0;

        if ($hitpoints < $lvl4) {
            return 'hech4';
        } elseif ($hitpoints < $lvl3) {
            return 'hech3';
        } elseif ($hitpoints < $lvl2) {
            return 'hech2';
        } elseif ($hitpoints <= $lvl1) {
            return 'hech1';
        }
        return false;
    }

    /**
     * Determina el grupo principal de una carta (win, ter, aer, dew, hech).
     *
     * Clasifica la carta basándose en su tipo, objetivo principal y estadísticas (DPS, HP).
     *
     * @param object $card La carta a clasificar.
     * @return string El grupo al que pertenece la carta ('win', 'ter', 'aer', 'dew', 'hech').
     * @throws \Exception Si el tipo de carta no se reconoce o falta información esencial.
     */
    private function getGroupCard(object $card): string
    {
        // Ensure necessary properties exist and have default values
        $dps = $card->dps ?? 0;
        $hitpoints = $card->hitpoints ?? 0;
        $type = $card->type ?? 'Unknown';
        $attackTargets = $card->Attack ?? ['null', 'null']; // Default to array with 'null'

        // Ensure constants are loaded
        $minDpsDew = $this->constants->MIN_DPS_DEFENSE_WIN_CONDITION ?? 200;
        $minHpDew = $this->constants->MIN_HITPOINTS_DEFENSE_WIN_CONDITION ?? 1000;
        $minDpsBuildingDew = $this->constants->MIN_DPS_DEFENSE_BUILDING ?? 200;

        // Determine group
        if ($type == 'Spell') {
            return 'hech';
        }

        // Check for Defensive Win Condition (dew) - High stats troop/building
        if (
            $type != 'Tower' && // Exclude King/Princess Towers
            (
                ($type == 'Building' && $dps > $minDpsBuildingDew) ||
                ($type != 'Building' && $dps > $minDpsDew && $hitpoints > $minHpDew && (in_array('ter', $attackTargets) || in_array('aer', $attackTargets)))
            )
        ) {
            return 'dew';
        }

        // Check for Win Condition (win) - Primarily targets structures
        if (in_array('est', $attackTargets)) {
            return 'win';
        }

        // Check for Terrestrial (ter) - Targets ground, not a Tower
        if (in_array('ter', $attackTargets) && $type != 'Tower') {
            return 'ter';
        }

        // Check for Aerial (aer) - Targets air OR is a Tower card (Princess Tower)
        if (in_array('aer', $attackTargets) || $type == 'Tower') {
            return 'aer';
        }


        // Fallback or error if no group is matched
        // This might indicate an issue with card data or logic
        // Log warning instead of throwing exception to allow analysis to continue
        error_log("Card '{$card->name}' (Type: {$type}, Targets: " . implode(',', $attackTargets) . ") could not be grouped.");
        return 'ter'; // Default to 'ter' as a fallback, or consider 'unknown'
        // throw new \Exception('Tipo de carta "' . $card->name . '" no reconocido o datos insuficientes para agrupar.');
    }


    /**
     * Determina los porcentajes de rol 'attack' y 'defense' de una carta.
     *
     * Evalúa múltiples atributos de la carta (objetivo, velocidad, tipo de ataque, rango, tipo de carta, habilidades especiales)
     * para asignar puntuaciones de ataque y defensa, que luego se normalizan a porcentajes.
     *
     * @param object $card La carta a evaluar.
     * @return array Un array asociativo con los porcentajes 'attack' y 'defense' (ej. ['attack' => 70, 'defense' => 30]).
     */
    private function getCardBatleRole(object $card): array
    {
        $attackScore = 50.0; // Start with a baseline assumption of 50/50
        $defenseScore = 50.0;

        // --- Adjust scores based on attributes ---

        // 1. Primary Target (Attack[0]) - Strong indicator
        $primaryTarget = $card->Attack[0] ?? 'null';
        if ($primaryTarget == 'est') { // Targets buildings
            $attackScore += 25;
            $defenseScore -= 15;
        } elseif ($primaryTarget == 'ter' || $primaryTarget == 'aer') { // Targets troops/air
            $defenseScore += 15;
            $attackScore -= 5;
        }
        // Consider Attack[1] if it exists and differs
        $secondaryTarget = $card->Attack[1] ?? 'null';
        if ($secondaryTarget != 'null' && $secondaryTarget != $primaryTarget) {
            if ($secondaryTarget == 'est') {
                $attackScore += 5;
            } // Slight attack boost if secondary is building
            else {
                $defenseScore += 5;
            } // Slight defense boost if secondary is troop/air
        }


        // 2. Movement Speed
        $speed = $card->speed ?? 'Medium';
        if ($speed == 'Very Fast') {
            $attackScore += 15;
            $defenseScore -= 5;
        } elseif ($speed == 'Fast') {
            $attackScore += 8;
        } elseif ($speed == 'Slow') {
            $defenseScore += 8;
            $attackScore -= 3;
        } elseif ($speed == 'Very Slow') {
            $defenseScore += 12;
            $attackScore -= 5;
        }

        // 3. Attack Speed (Hitspeed)
        $hitspeed = $card->hitspeed ?? 1.5; // Assume average if not present
        if ($hitspeed < 1.0) {
            $defenseScore += 8;
        } // Good vs swarms, interrupts
        elseif ($hitspeed > 2.0) {
            $attackScore += 5;
        } // Often high damage per hit, good for burst

        // 4. Damage Type
        $typeAttack = $card->TypeAttack ?? 'unique';
        if ($typeAttack == 'splash') {
            $defenseScore += 15; // Very important for defense against swarms
            $attackScore += 5;  // Can help clear path during offense
        } else { // unique
            $attackScore += 5; // Often better single target dps for offense
            $defenseScore += 5; // Good vs tanks on defense
        }

        // 5. Range (Consider radius for spells/splash)
        $range = $card->range ?? ($card->radius ?? 0);
        if ($range > 5) {
            $attackScore += 5;
            $defenseScore += 8;
        } // Safety for both roles
        elseif ($range < 2 && $range > 0) {
            $defenseScore += 5;
        } // Melee often better suited for defense due to HP/DPS, or needs tank on offense

        // 6. Card Type (Building/Spell specific adjustments)
        if ($card->type == 'Building') {
            if ($card->territory == 'Restricted') { // Defensive building (Tesla, Cannon, Inferno Tower)
                $defenseScore += 25;
                $attackScore -= 15;
            } else { // Spawner or offensive building (Goblin Hut, Furnace, X-Bow, Mortar)
                $defenseScore += 10; // Provides defensive value over time
                $attackScore += 10; // Can apply pressure or act as win condition
            }
        } elseif ($card->type == 'Spell') {
            // Spells: Damage spells lean attack, control/utility spells lean defense.
            if (($card->damage > 0 || property_exists($card, 'towerDamage')) && $card->damage > 50) { // Damage spell (consider threshold)
                $attackScore += 15; // Good for finishing towers or clearing path
                $defenseScore += 5; // Can be used defensively too
            } else { // Assume utility/control spell (Log, Zap, Freeze, Tornado, Clone, Heal Spirit, Rage)
                $defenseScore += 15; // Primarily defensive or support role
                $attackScore -= 5;
            }
            // Specific spell overrides could be added (e.g., Rage more offensive, Freeze more defensive)
        }

        // 7. Special Abilities
        $specialKeys = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
        $specialEvoKeys = ($card->evolution && $card->specialEvo) ? array_keys(json_decode(json_encode($card->specialEvo), true)) : [];
        $allSpecialKeys = array_unique(array_merge($specialKeys, $specialEvoKeys));

        if (in_array('charge', $allSpecialKeys) || in_array('dash', $allSpecialKeys) || in_array('jump', $allSpecialKeys)) {
            $attackScore += 15;
        } // Strong offensive initiation
        if (in_array('GenerationUnits', $allSpecialKeys)) {
            $defenseScore += 12;
            $attackScore += 3;
        } // Primarily defensive value
        if (in_array('stun', $allSpecialKeys) || in_array('slow', $allSpecialKeys) || in_array('knockback', $allSpecialKeys) || in_array('freeze', $allSpecialKeys) || $card->name == 'Tornado') {
            $defenseScore += 15;
        } // Strong control = defense
        if (in_array('dpsIncrement', $allSpecialKeys)) {
            $attackScore += 8;
            $defenseScore += 8;
        } // Good vs high HP targets (offense/defense)
        if ($card->suicide ?? false) {
            $attackScore += 10;
            $defenseScore -= 15;
        } // Primarily offensive (Wall Breakers, Golemite death)

        // 8. Air Attack Capability
        if (in_array('aer', $card->Attack)) {
            $defenseScore += 15; // Crucial defensive capability
        } else {
            // If it can't attack air, it's less defensively versatile (unless it's a building/spell)
            if ($card->type != 'Spell' && $card->type != 'Building') {
                $defenseScore -= 10;
            }
        }

        // --- Normalization ---
        // Ensure scores are not negative
        $attackScore = max(0, $attackScore);
        $defenseScore = max(0, $defenseScore);

        $totalScore = $attackScore + $defenseScore;

        if ($totalScore == 0) {
            // Avoid division by zero, return default 50/50 if no factors applied
            return ['attack' => 50, 'defense' => 50];
        }

        $attackPercent = round(($attackScore / $totalScore) * 100);
        $defensePercent = 100 - $attackPercent; // Ensure it sums to 100

        return ['attack' => $attackPercent, 'defense' => $defensePercent];
    }


    /**
     * Analiza el mazo para determinar la cobertura de diferentes tipos de defensa.
     *
     * Evalúa las cartas del mazo para identificar si proporcionan defensa contra
     * amenazas aéreas, tanques, enjambres, etc., así como capacidades de control
     * y daño directo mediante hechizos.
     *
     * @return array Un array asociativo donde las claves son los tipos de defensa
     *               (ej. 'anti_air', 'anti_tank') y los valores son booleanos
     *               indicando si el mazo cubre ese tipo de defensa.
     * @throws \Exception Si ocurre un error al acceder a las propiedades de las cartas o decodificar JSON.
     */
    public function getDefenseTypesCoverage(): array
    {
        $defenseCoverage = [
            'anti_air' => ['isCovered' => false, 'cards' => []],
            'anti_tank' => ['isCovered' => false, 'cards' => []], // Alto daño único o incremental
            'splash_ground' => ['isCovered' => false, 'cards' => []], // Daño de área terrestre
            'splash_air' => ['isCovered' => false, 'cards' => []], // Daño de área aéreo
            'swarm_defense' => ['isCovered' => false, 'cards' => []], // Defensa contra enjambres (multi-unidad, splash, rápido)
            'building_defense' => ['isCovered' => false, 'cards' => []], // Estructuras defensivas
            'direct_damage_spell' => ['isCovered' => false, 'cards' => []], // Hechizos de daño directo a torre
            'control' => ['isCovered' => false, 'cards' => []], // Aturdir, ralentizar, empujar, congelar, etc.
        ];

        // Umbral de DPS para considerar anti-tanque (ajustable)
        $antiTankDpsThreshold = $this->constants->ANTI_TANK_DPS_THRESHOLD ?? 300; // Example, adjust as needed
        $antiTankDamageThreshold = $this->constants->ANTI_TANK_DAMAGE_THRESHOLD ?? 500; // Example for high single hit

        // Atributos de control
        $controlAttributes = $this->constants->CONTROL_ATTRIBUTES_LIST ?? ['stun', 'slow', 'knockback', 'freeze', 'pull', 'charge', 'dash', 'jump']; // Include charge/dash/jump which can interrupt/distract

        foreach (array_merge($this->Deck->Cards, $this->Deck->CardsEvo) as $card) {
            $specialKeys = $card->special ? array_keys(json_decode(json_encode($card->special), true)) : [];
            $specialEvoKeys = ($card->evolution && $card->specialEvo) ? array_keys(json_decode(json_encode($card->specialEvo), true)) : [];
            $allSpecialKeys = array_unique(array_merge($specialKeys, $specialEvoKeys));
            $cardName = $card->name; // For adding to the list

            // Anti-Aéreo
            if (in_array('aer', $card->Attack)) {
                $defenseCoverage['anti_air']['isCovered'] = true;
                $defenseCoverage['anti_air']['cards'][] = $cardName;
            }

            // Anti-Tanque (Mejorado)
            $isAntiTank = false;
            if ($card->type != 'Spell') {
                if (
                    ($card->TypeAttack == 'unique' && ($card->dps ?? 0) >= $antiTankDpsThreshold) || // Alto DPS único
                    (($card->damage ?? 0) >= $antiTankDamageThreshold) || // Alto daño por golpe
                    (in_array('dpsIncrement', $allSpecialKeys)) || // Daño incremental
                    ($card->units > 3 && ($card->dps ?? 0) * $card->units >= $antiTankDpsThreshold * 1.5) // Swarm with high total DPS (e.g., Skarmy, Goblins)
                ) {
                    $isAntiTank = true;
                }
            } elseif ($cardName == 'Rocket' || $cardName == 'Lightning') { // Hechizos de alto daño
                $isAntiTank = true;
            }
            if ($isAntiTank) {
                $defenseCoverage['anti_tank']['isCovered'] = true;
                $defenseCoverage['anti_tank']['cards'][] = $cardName;
            }

            // Salpicadura Terrestre
            if ($card->TypeAttack == 'splash' && in_array('ter', $card->Attack)) {
                $defenseCoverage['splash_ground']['isCovered'] = true;
                $defenseCoverage['splash_ground']['cards'][] = $cardName;
            }

            // Salpicadura Aérea
            if ($card->TypeAttack == 'splash' && in_array('aer', $card->Attack)) {
                $defenseCoverage['splash_air']['isCovered'] = true;
                $defenseCoverage['splash_air']['cards'][] = $cardName;
            }

            // Defensa contra Enjambres
            $isSwarmDefense = false;
            if ($card->type != 'Spell') {
                if (
                    ($card->units > 1 && ($card->hitspeed ?? 2) < 1.5) || // Multi-unidad rápida
                    ($card->TypeAttack == 'splash') || // Salpicadura
                    (($card->hitspeed ?? 2) < 0.8) || // Ataque muy rápido
                    in_array('GenerationUnits', $allSpecialKeys) // Genera unidades
                ) {
                    $isSwarmDefense = true;
                }
            } elseif (in_array($cardName, ['Arrows', 'Zap', 'The Log', 'Barbarian Barrel', 'Snowball', 'Tornado', 'Poison'])) { // Spells effective vs swarm
                $isSwarmDefense = true;
            }
            if ($isSwarmDefense) {
                $defenseCoverage['swarm_defense']['isCovered'] = true;
                $defenseCoverage['swarm_defense']['cards'][] = $cardName;
            }

            // Estructura Defensiva
            $isBuildingDefense = false;
            if ($card->type == 'Building') {
                if ($card->territory == 'Restricted' || in_array('GenerationUnits', $allSpecialKeys)) {
                    $isBuildingDefense = true;
                }
            }
            if ($isBuildingDefense) {
                $defenseCoverage['building_defense']['isCovered'] = true;
                $defenseCoverage['building_defense']['cards'][] = $cardName;
            }

            // Hechizo de Daño Directo (a Torres)
            if ($card->type == 'Spell' && property_exists($card, 'towerDamage') && $card->towerDamage > 0) {
                $defenseCoverage['direct_damage_spell']['isCovered'] = true;
                $defenseCoverage['direct_damage_spell']['cards'][] = $cardName;
            }

            // Control
            $hasControl = false;
            foreach ($controlAttributes as $controlAttr) {
                if (in_array($controlAttr, $allSpecialKeys)) {
                    $hasControl = true;
                    break;
                }
            }
            // Specific control spells
            if ($card->type == 'Spell' && in_array($cardName, ['Tornado', 'The Log', 'Barbarian Barrel', 'Snowball', 'Zap', 'Freeze', 'Ice Spirit', 'Electro Spirit'])) { // Added spirits, Zap
                $hasControl = true;
            }
            if ($hasControl) {
                $defenseCoverage['control']['isCovered'] = true;
                $defenseCoverage['control']['cards'][] = $cardName;
            }

        }

        // Remove duplicates from card lists
        foreach ($defenseCoverage as &$coverageInfo) {
            if (isset($coverageInfo['cards'])) {
                $coverageInfo['cards'] = array_values(array_unique($coverageInfo['cards']));
            }
        }
        unset($coverageInfo);


        return $defenseCoverage;
    }

    /**
     * Analiza las debilidades del mazo basándose en puntajes y cobertura defensiva.
     *
     * Evalúa los puntajes totales de Sinergia, Versatilidad, Ataque y Defensa contra umbrales bajos,
     * y verifica la cobertura de tipos de defensa para identificar vulnerabilidades.
     *
     * @return array Un array asociativo con la clave 'weaknesses',
     *               cuyo valor es otro array asociativo donde las claves son los nombres
     *               de las debilidades y los valores son las sugerencias correspondientes.
     */
    public function getDeckWeaknesses(): array
    {
        $suggestions = [];
        // Umbral para considerar una puntuación como debilidad. Podría moverse a Constants.json.
        $weaknessThreshold = $this->constants->WEAKNESS_THRESHOLD ?? 35; // Increased threshold slightly

        // Obtener puntajes específicos y cobertura
        $attackScores = $this->getBattleRolesScores('attack');
        $defenseScores = $this->getBattleRolesScores('defense');
        $defenseCoverage = $this->getDefenseTypesCoverage();

        // Mapa de sugerencias dinámicas
        $suggestionMap = [
            // Debilidades de Roles de Batalla (Ataque)
            'attack_low_dps' => sprintf('El DPS promedio de ataque es bajo (%d). Considera cartas ofensivas con más daño como Mini P.E.K.K.A., Leñador, Príncipe o unidades aéreas fuertes.', $attackScores['scoreDps']),
            'attack_low_hitpoints' => sprintf('Las tropas de ataque tienen pocos puntos de vida promedio (%d). Añade un tanque (Gigante, Golem, Sabueso) o semi-tanque (Valquiria, Caballero) para protegerlas.', $attackScores['scoreHitpoints']),
            'attack_low_range' => sprintf('El alcance promedio de ataque es bajo (%d). Incluye cartas de mayor alcance como Mosquetera, Arquero Mágico o Princesa para apoyar desde atrás.', $attackScores['scoreRange']),
            // Debilidades de Roles de Batalla (Defensa)
            'defense_low_dps' => sprintf('El DPS promedio de defensa es bajo (%d). Añade cartas defensivas con buen daño como Cazador, Mini P.E.K.K.A., Torre Infernal o unidades con daño de área.', $defenseScores['scoreDps']),
            'defense_low_hitpoints' => sprintf('Las cartas defensivas tienen pocos puntos de vida promedio (%d). Usa estructuras más resistentes (Tesla, Torre Infernal) o tropas robustas como Valquiria, Megaesbirro o Caballero.', $defenseScores['scoreHitpoints']),
            'defense_low_range' => sprintf('El alcance promedio de defensa es bajo (%d). Considera cartas defensivas de mayor alcance como Torre Tesla, Mago de Hielo, Arquero Mágico o Mosquetera.', $defenseScores['scoreRange']),
            // Debilidades de Cobertura Defensiva
            'coverage_missing_anti_air' => 'Falta defensa anti-aérea fiable. Incluye cartas como Esbirros, Megaesbirro, Mosquetera, Mago Eléctrico, Dragón Infernal o estructuras que ataquen aire.',
            'coverage_missing_anti_tank' => 'Vulnerable a tanques. Añade cartas con alto DPS único (Cazador, Mini P.E.K.K.A., P.E.K.K.A), daño incremental (Torre Infernal, Dragón Infernal) o enjambres (Ejército Esqueletos, Pandilla).',
            'coverage_missing_splash_ground' => 'Débil contra unidades terrestres agrupadas (Ejército, Barril Duendes). Usa cartas con daño de área terrestre como Valquiria, Bombardero, Mago, Tronco, Flechas o Bola de Fuego.',
            'coverage_missing_splash_air' => 'Débil contra unidades aéreas agrupadas (Esbirros, Murciélagos). Considera cartas como Dragón Bebé, Mago, Bruja, Flechas, Descarga, Bola de Nieve o Tornado.',
            'coverage_missing_swarm_defense' => 'Vulnerable a enjambres (terrestres o aéreos). Incluye cartas con daño de área rápido (Flechas, Descarga, Tronco, Bola Nieve), unidades de salpicadura (Valquiria, Mago, Bombardero) o multi-unidades rápidas.',
            'coverage_missing_building_defense' => 'Falta una estructura defensiva para distraer unidades que atacan edificios (Montapuercos, Gigante). Añade edificios como Torre Tesla, Torre Infernal, Cañón o Lápida.',
            'coverage_missing_direct_damage_spell' => 'Sin hechizo de daño directo a torres para rematar partidas. Considera Bola de Fuego, Veneno, Rayo o Cohete.',
            'coverage_missing_control' => 'Falta control para redirigir, ralentizar o reiniciar tropas enemigas. Incluye cartas como Tronco, Descarga, Bola Nieve, Tornado, Hielo, Espíritu de Hielo/Eléctrico o Mago de Hielo/Eléctrico.'
        ];


        // Evaluar puntajes específicos de roles de ataque
        if ($attackScores['scoreDps'] < $weaknessThreshold) {
            $suggestions['Bajo DPS de Ataque'] = $suggestionMap['attack_low_dps'];
        }
        if ($attackScores['scoreHitpoints'] < $weaknessThreshold) {
            $suggestions['Bajos PV de Ataque'] = $suggestionMap['attack_low_hitpoints'];
        }
        if ($attackScores['scoreRange'] < $weaknessThreshold) {
            $suggestions['Bajo Rango de Ataque'] = $suggestionMap['attack_low_range'];
        }

        // Evaluar puntajes específicos de roles de defensa
        if ($defenseScores['scoreDps'] < $weaknessThreshold) {
            $suggestions['Bajo DPS de Defensa'] = $suggestionMap['defense_low_dps'];
        }
        if ($defenseScores['scoreHitpoints'] < $weaknessThreshold) {
            $suggestions['Bajos PV de Defensa'] = $suggestionMap['defense_low_hitpoints'];
        }
        if ($defenseScores['scoreRange'] < $weaknessThreshold) {
            $suggestions['Bajo Rango de Defensa'] = $suggestionMap['defense_low_range'];
        }

        // Evaluar tipos de cobertura defensiva
        if (empty($defenseCoverage['anti_air']['isCovered'])) {
            $suggestions['Falta Cobertura Anti-Aérea'] = $suggestionMap['coverage_missing_anti_air'];
        }
        if (empty($defenseCoverage['anti_tank']['isCovered'])) {
            $suggestions['Falta Cobertura Anti-Tanque'] = $suggestionMap['coverage_missing_anti_tank'];
        }
        if (empty($defenseCoverage['splash_ground']['isCovered'])) {
            $suggestions['Falta Cobertura Terrestre de Área'] = $suggestionMap['coverage_missing_splash_ground'];
        }
        if (empty($defenseCoverage['splash_air']['isCovered'])) {
            $suggestions['Falta Cobertura Aérea de Área'] = $suggestionMap['coverage_missing_splash_air'];
        }
        if (empty($defenseCoverage['swarm_defense']['isCovered'])) {
            $suggestions['Falta Defensa Contra Enjambres'] = $suggestionMap['coverage_missing_swarm_defense'];
        }
        if (empty($defenseCoverage['building_defense']['isCovered'])) {
            $suggestions['Falta Edificio Defensivo'] = $suggestionMap['coverage_missing_building_defense'];
        }
        if (empty($defenseCoverage['direct_damage_spell']['isCovered'])) {
            $suggestions['Falta Hechizo de Daño Directo'] = $suggestionMap['coverage_missing_direct_damage_spell'];
        }
        if (empty($defenseCoverage['control']['isCovered'])) {
            $suggestions['Falta Control'] = $suggestionMap['coverage_missing_control'];
        }

        // Ya no se verifican las puntuaciones de Sinergia y Versatilidad aquí para debilidades,
        // según el requisito de vincular las debilidades directamente a los roles de batalla y la cobertura.

        return [
            'weaknesses' => $suggestions
        ];
    }


    /**
     * Identifica el arquetipo del mazo actual basado en definiciones predefinidas.
     *
     * Carga las definiciones de arquetipos desde un archivo JSON y compara las cartas del mazo,
     * el coste promedio de elixir y el coste del ciclo corto con las condiciones de cada arquetipo.
     *
     * @return array Un array asociativo con:
     *               - 'archetype': El nombre del arquetipo principal ('Log Bait', 'Híbrido', 'Desconocido').
     *               - 'details': Un array con información adicional (ej. nombres de arquetipos coincidentes si es 'Híbrido').
     * @throws \Exception Si el archivo de arquetipos no existe o no se puede decodificar.
     */
    public function identifyArchetype(): array
    {
        $archetypesPath = __DIR__ . '/../../App/Data/tools/DeckAnalyzer/archetypes.json';
        $archetypes = json_decode(file_get_contents($archetypesPath), true);
        $deckCards = array_map(fn($card) => $card->name, array_merge($this->Deck->Cards, $this->Deck->CardsEvo));
        $averageElixir = $this->getAverageElixirCost();
        $shortCycleCost = $this->getShortCycle();
        $matchedArchetypes = [];

        foreach ($archetypes as $archetypeName => $definition) {
            // Check for key cards: ALL key cards must be present
            if (!empty($definition['keyCards']) && array_diff($definition['keyCards'], $deckCards)) {
                continue; // Missing a key card
            }

            // Check for alternative key cards: AT LEAST ONE set of alternatives must be present
            $alternativeMatch = true; // Assume true if no alternatives defined
            if (!empty($definition['alternativeKeyCards'])) {
                $alternativeMatch = false; // Reset to false, needs at least one match
                foreach ($definition['alternativeKeyCards'] as $alternativeSet) {
                    if (empty(array_diff($alternativeSet, $deckCards))) {
                        $alternativeMatch = true; // Found a matching set
                        break;
                    }
                }
            }
            if (!$alternativeMatch) {
                continue; // Did not meet alternative key card requirement
            }

            // Check elixir range
            if (
                $averageElixir < ($definition['elixirRange']['min'] ?? 0) ||
                $averageElixir > ($definition['elixirRange']['max'] ?? 10)
            ) {
                continue;
            }

            // Check cycle cost range
            if (
                $shortCycleCost < ($definition['cycleCostRange']['min'] ?? 0) ||
                $shortCycleCost > ($definition['cycleCostRange']['max'] ?? 20)
            ) {
                continue;
            }

            // Check for incompatible cards
            if (!empty($definition['incompatibleCards']) && array_intersect($definition['incompatibleCards'], $deckCards)) {
                continue; // Contains an incompatible card
            }

            $matchedArchetypes[] = $archetypeName;
        }

        // Determine the final result
        $matchCount = count($matchedArchetypes);
        if ($matchCount === 1) {
            return ['archetype' => $matchedArchetypes[0], 'details' => []];
        } elseif ($matchCount > 1) {
            // If multiple archetypes match, prioritize based on key card count? Or just call it Hybrid?
            // For now, keep it as Hybrid. Could add prioritization later.
            return ['archetype' => 'Híbrido', 'details' => $matchedArchetypes];
        } else {
            // Could add logic here to identify characteristics if 'Desconocido'
            return ['archetype' => 'Desconocido', 'details' => []];
        }
    }

}
